import os
from flask import Blueprint, render_template, redirect, url_for
from app.utils.helpers import list_categories, list_parent_categories

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    # Try to get parent categories first
    try:
        parent_categories = list_parent_categories()
        if parent_categories:
            categories = parent_categories
        else:
            # Fallback to regular categories
            categories = sorted(list_categories())
    except Exception as e:
        # Fallback to regular categories if there's an error
        categories = sorted(list_categories())
    
    return render_template('index.html', categories=categories)

@main_bp.route('/admin', methods=['GET'])
def admin_redirect():
    """Redirect /admin to /admin/dashboard"""
    return redirect(url_for('admin.admin_dashboard'))
