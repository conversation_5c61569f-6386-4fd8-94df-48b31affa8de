#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Migration script to add parent categories functionality.
This script creates the parent_categories table and modifies the categories table
to support hierarchical category organization.
"""

import os
import sqlite3
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def get_database_path():
    """Get the main database path."""
    # Try to find the main database
    possible_paths = [
        "./erdb_main.db",
        "./data/erdb.db",
        "./instance/erdb_main.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # If no existing database found, use the default
    return "./erdb_main.db"

def create_parent_categories_tables():
    """Create the parent_categories table and modify categories table."""
    db_path = get_database_path()
    logger.info(f"Using database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create parent_categories table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS parent_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                display_order INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        logger.info("Created parent_categories table")
        
        # Check if parent_category_id column exists in categories table
        cursor.execute("PRAGMA table_info(categories)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'parent_category_id' not in columns:
            cursor.execute('''
                ALTER TABLE categories 
                ADD COLUMN parent_category_id INTEGER 
                REFERENCES parent_categories(id) ON DELETE SET NULL
            ''')
            logger.info("Added parent_category_id column to categories table")
        
        # Create indexes for better performance
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_parent_categories_active ON parent_categories(is_active)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_parent_categories_display_order ON parent_categories(display_order)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_category_id)")
        
        # Insert default parent categories
        default_parent_categories = [
            ('Technical Publications', 'Technical and research publications including journals and reports', 1),
            ('Semi-Technical Publications', 'Publications aimed at semi-technical audiences', 2),
            ('Others', 'Other categories and miscellaneous content', 3)
        ]
        
        for name, description, display_order in default_parent_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO parent_categories (name, description, display_order)
                VALUES (?, ?, ?)
            ''', (name, description, display_order))
        
        logger.info("Inserted default parent categories")
        
        # Get existing categories and assign them to appropriate parent categories
        cursor.execute("SELECT id, name FROM categories")
        existing_categories = cursor.fetchall()
        
        # Define category mappings
        category_mappings = {
            'CANOPY': 'Technical Publications',
            'RISE': 'Technical Publications',
            'INFO JOURNAL': 'Semi-Technical Publications',
            'GAD REACH': 'Semi-Technical Publications',
            'MANUAL': 'Others'
        }
        
        # Assign categories to parent categories
        for category_id, category_name in existing_categories:
            parent_name = category_mappings.get(category_name.upper(), 'Others')
            
            cursor.execute('''
                SELECT id FROM parent_categories WHERE name = ?
            ''', (parent_name,))
            
            parent_result = cursor.fetchone()
            if parent_result:
                parent_id = parent_result[0]
                cursor.execute('''
                    UPDATE categories 
                    SET parent_category_id = ? 
                    WHERE id = ?
                ''', (parent_id, category_id))
                logger.info(f"Assigned category '{category_name}' to parent category '{parent_name}'")
        
        conn.commit()
        logger.info("Successfully created parent categories tables and migrated existing data")
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating parent categories tables: {str(e)}")
        if conn:
            conn.rollback()
        return False
    
    finally:
        if conn:
            conn.close()

def verify_migration():
    """Verify that the migration was successful."""
    db_path = get_database_path()
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if parent_categories table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='parent_categories'")
        if not cursor.fetchone():
            logger.error("parent_categories table not found")
            return False
        
        # Check if parent_category_id column exists in categories table
        cursor.execute("PRAGMA table_info(categories)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'parent_category_id' not in columns:
            logger.error("parent_category_id column not found in categories table")
            return False
        
        # Check if default parent categories were created
        cursor.execute("SELECT COUNT(*) FROM parent_categories")
        parent_count = cursor.fetchone()[0]
        if parent_count == 0:
            logger.error("No parent categories found")
            return False
        
        # Check if categories were assigned to parent categories
        cursor.execute("SELECT COUNT(*) FROM categories WHERE parent_category_id IS NOT NULL")
        assigned_count = cursor.fetchone()[0]
        logger.info(f"Found {parent_count} parent categories and {assigned_count} assigned categories")
        
        return True
        
    except Exception as e:
        logger.error(f"Error verifying migration: {str(e)}")
        return False
    
    finally:
        if conn:
            conn.close()

def main():
    """Main migration function."""
    logger.info("Starting parent categories migration...")
    
    # Create tables and migrate data
    if create_parent_categories_tables():
        logger.info("Migration completed successfully")
        
        # Verify the migration
        if verify_migration():
            logger.info("Migration verification passed")
        else:
            logger.error("Migration verification failed")
            return False
    else:
        logger.error("Migration failed")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("Parent categories migration completed successfully!")
    else:
        print("Parent categories migration failed!")
        exit(1)
