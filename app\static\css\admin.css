/* Admin Dashboard Styles - DarkPan Theme */

/* General Styles */
body {
    overflow-x: hidden;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-family: var(--font-family-sans);
    position: relative;
    margin: 0;
    padding: 0;
}

/* Sidebar Styles - DarkPan */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 250px;
    height: 100vh;
    overflow-y: auto;
    background: var(--sidebar-bg);
    transition: 0.5s;
    z-index: var(--z-index-fixed);
}

.sidebar-sticky {
    position: relative;
    height: 100%;
    padding-top: 0;
    overflow-x: hidden;
    overflow-y: auto;
    scrollbar-width: thin;
}

.sidebar-sticky::-webkit-scrollbar {
    width: 5px;
}

.sidebar-sticky::-webkit-scrollbar-track {
    background: var(--bg-light);
}

.sidebar-sticky::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: var(--border-radius);
}

/* Sidebar Brand */
.sidebar-brand {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    background: var(--sidebar-bg);
    padding: 0 var(--spacing-4);
    border-bottom: 1px solid var(--border-color);
}

.sidebar-brand h3 {
    color: var(--text-primary);
    font-size: var(--font-size-xl);
    margin: 0;
    padding: 0;
}

.sidebar-brand i {
    color: var(--primary-500);
    font-size: var(--font-size-xl);
}

/* Sidebar Navigation */
.sidebar .navbar {
    padding: 0;
    background-color: transparent !important;
    box-shadow: none;
}

.sidebar .navbar-nav {
    width: 100%;
}

.sidebar .nav-item {
    width: 100%;
    margin-bottom: var(--spacing-1);
}

.sidebar .nav-link {
    color: var(--sidebar-text);
    padding: var(--spacing-3) var(--spacing-4);
    font-weight: var(--font-weight-medium);
    border-radius: 0;
    margin: 0;
    display: flex;
    align-items: center;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover-bg);
    color: var(--primary-500);
}

.sidebar .nav-link.active {
    color: var(--sidebar-active-text);
    background-color: var(--sidebar-active-bg);
}

.sidebar .nav-link i {
    margin-right: var(--spacing-3);
    width: var(--spacing-4);
    text-align: center;
    font-size: 1rem;
}

/* Sidebar Dropdown */
.sidebar .dropdown-toggle {
    position: relative;
    padding-right: var(--spacing-5);
    cursor: pointer;
    user-select: none;
    pointer-events: auto;
}

.sidebar .dropdown-toggle::after {
    position: absolute;
    top: 50%;
    right: var(--spacing-4);
    transform: translateY(-50%);
    border: none;
    content: "\f105";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    transition: transform var(--transition-fast);
}

.sidebar .dropdown-toggle[aria-expanded="true"]::after {
    transform: translateY(-50%) rotate(90deg);
}

.sidebar .dropdown-menu {
    position: static !important;
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    transform: none !important;
    box-shadow: none;
    float: none;
    width: 100%;
    display: none !important;
    overflow: hidden;
}

.sidebar .dropdown-menu.show {
    display: block !important;
}

.sidebar .dropdown-item {
    color: var(--sidebar-text);
    padding: var(--spacing-2) var(--spacing-4);
    padding-left: var(--spacing-6);
    border-radius: 0;
    cursor: pointer;
    pointer-events: auto;
    text-decoration: none;
    display: block;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.sidebar .dropdown-item:hover {
    background-color: var(--sidebar-hover-bg);
    color: var(--primary-500);
    text-decoration: none;
}

.sidebar .dropdown-item.active {
    color: var(--sidebar-active-text);
    background-color: var(--sidebar-active-bg);
}

/* Ensure sidebar navigation is clickable */
.sidebar .nav-item {
    position: relative;
    z-index: 1;
}

.sidebar .nav-link {
    position: relative;
    z-index: 2;
    pointer-events: auto;
    cursor: pointer;
}

/* Sidebar Section Headers */
.sidebar-heading {
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.1rem;
    padding: var(--spacing-3) var(--spacing-4) var(--spacing-2);
    color: var(--text-muted);
    font-weight: var(--font-weight-semibold);
}

/* Collapsible Sections */
.sidebar-section {
    margin-bottom: var(--spacing-3);
    padding-bottom: var(--spacing-1);
}

.sidebar-section:last-child {
    border-bottom: none;
}

.sidebar-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-3) var(--spacing-4);
    font-weight: var(--font-weight-semibold);
    color: var(--sidebar-text);
    cursor: pointer;
    border-radius: 0;
    margin: 0;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.sidebar-section-header:hover {
    background-color: var(--sidebar-hover-bg);
    color: var(--primary-500);
}

.sidebar-section-header.active {
    background-color: var(--sidebar-hover-bg);
    color: var(--primary-500);
}

.sidebar-section-header i.section-icon {
    margin-right: var(--spacing-3);
    width: var(--spacing-4);
    text-align: center;
}

.sidebar-section-header i.toggle-icon {
    font-size: 0.8rem;
    transition: transform var(--transition-fast);
}

.sidebar-section-header[aria-expanded="true"] i.toggle-icon {
    transform: rotate(90deg);
}

.sidebar-section-content {
    padding-left: var(--spacing-3);
    margin-top: var(--spacing-1);
}

/* DarkPan Content & Navbar */
.content {
    position: relative;
    width: 100%;
    transition: 0.5s;
}

/* Top Navbar - DarkPan */
.navbar {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-4);
    height: 60px;
    background: var(--navbar-bg) !important;
    box-shadow: var(--shadow);
}

.navbar-brand {
    font-weight: var(--font-weight-semibold);
    color: var(--navbar-text) !important;
    display: none;
}

.sidebar-toggler {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--bg-light);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
}

/* Navbar Search */
.navbar-search {
    position: relative;
    width: 300px;
    margin: 0 var(--spacing-4);
}

.navbar-search input {
    width: 100%;
    height: 40px;
    padding: 0 var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--input-text);
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
}

.navbar-search button {
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
}

/* User Profile Dropdown */
.user-profile-dropdown .dropdown-toggle::after {
    display: none;
}

.user-profile-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius-circle);
    background-color: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-right: var(--spacing-2);
}

.user-profile-dropdown .dropdown-menu {
    margin-top: var(--spacing-2);
    border: 1px solid var(--border-color);
    background-color: var(--bg-card);
    box-shadow: var(--shadow);
}

.user-profile-dropdown .dropdown-item {
    color: var(--text-primary);
    padding: var(--spacing-2) var(--spacing-3);
}

.user-profile-dropdown .dropdown-item:hover {
    background-color: var(--bg-light);
    color: var(--primary-500);
}

.user-profile-dropdown .dropdown-divider {
    border-top-color: var(--border-color);
}

/* Notifications */
.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    font-size: var(--font-size-xs);
    padding: 0.15rem 0.35rem;
    background-color: var(--primary-500);
    color: #FFFFFF;
    border-radius: var(--border-radius-circle);
}

/* Content Area */
.content-wrapper {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: var(--spacing-4);
    min-height: calc(100vh - 60px);
}

/* DarkPan Cards */
.card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-4);
}

.card-header {
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
}

.card-header h5, .card-header h6 {
    margin: 0;
}

.card-body {
    padding: var(--spacing-4);
}

.card-footer {
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--bg-card);
    border-top: 1px solid var(--border-color);
}

/* DarkPan Tables */
.table {
    color: var(--text-primary);
    margin-bottom: 0;
}

.table-dark {
    background-color: var(--bg-card);
}

.table-dark th {
    background-color: var(--table-header-bg);
    color: var(--text-primary);
    border-color: var(--table-border);
    padding: var(--spacing-3) var(--spacing-4);
    font-weight: var(--font-weight-medium);
}

.table-dark td {
    color: var(--text-secondary);
    border-color: var(--table-border);
    padding: var(--spacing-3) var(--spacing-4);
    vertical-align: middle;
}

.table-responsive {
    overflow-x: auto;
}

/* DarkPan Dashboard Components */

/* Back to Top Button */
.back-to-top {
    position: fixed;
    display: none;
    right: 30px;
    bottom: 30px;
    z-index: var(--z-index-fixed);
    width: 40px;
    height: 40px;
    background: var(--primary-500);
    color: #FFFFFF;
    border: none;
    border-radius: var(--border-radius);
    transition: background-color var(--transition-fast);
}

.back-to-top:hover {
    background: var(--primary-600);
}

/* DarkPan Progress Bar */
.progress {
    height: 10px;
    border-radius: var(--border-radius);
    background: var(--bg-light);
}

.progress .progress-bar {
    border-radius: var(--border-radius);
    background: var(--primary-500);
}

/* DarkPan Testimonial */
.testimonial-carousel .testimonial-item {
    padding: var(--spacing-4);
    border-radius: var(--border-radius);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
}

.testimonial-carousel .testimonial-item img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-circle);
    object-fit: cover;
}

/* DarkPan Footer */
.footer {
    padding: var(--spacing-4) 0;
    background: var(--bg-card);
    border-top: 1px solid var(--border-color);
}

/* DarkPan Form Elements */
.form-floating input.form-control {
    height: 55px;
    background: var(--input-bg);
    border-color: var(--input-border);
    color: var(--input-text);
}

.form-floating textarea.form-control {
    height: 120px;
    background: var(--input-bg);
    border-color: var(--input-border);
    color: var(--input-text);
}

.form-floating label {
    color: var(--text-secondary);
}

.form-floating input.form-control:focus,
.form-floating textarea.form-control:focus {
    border-color: var(--primary-500);
}

.form-check-input {
    background-color: var(--input-bg);
    border-color: var(--input-border);
}

.form-check-input:checked {
    background-color: var(--primary-500);
    border-color: var(--primary-500);
}

/* DarkPan Stat Cards */
.stat-card {
    position: relative;
    padding: var(--spacing-4);
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-4);
}

.stat-card .stat-icon {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    font-size: 2rem;
    color: var(--primary-500);
}

.stat-card .stat-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-1);
}

.stat-card .stat-title {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* Mobile Optimizations - DarkPan */
@media (min-width: 992px) {
    .sidebar {
        margin-left: 0;
    }

    .content {
        padding-left: 250px;
    }

    .content.open {
        padding-left: 250px;
    }
}

@media (max-width: 991.98px) {
    .sidebar {
        margin-left: -250px;
        width: 250px;
    }

    .sidebar.open {
        margin-left: 0;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    }

    .content {
        padding-left: 0;
        width: 100%;
    }

    .content.open {
        padding-left: 0;
    }

    .navbar-brand {
        display: block;
    }

    .navbar-search {
        width: 200px;
    }
}

@media (max-width: 767.98px) {
    .sidebar {
        margin-left: -250px;
        width: 250px;
    }

    .sidebar.open {
        margin-left: 0;
        width: 250px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    }

    .content {
        padding-left: 0;
        width: 100%;
    }

    .content.open {
        padding-left: 0;
    }

    .navbar-search {
        display: none;
    }

    .content-wrapper {
        padding: var(--spacing-3);
    }
}

/* Theme Toggle Button - DarkPan */
.theme-toggle {
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    color: var(--text-primary);
    transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
    background-color: var(--primary-500);
    color: #FFFFFF;
}

.theme-icon {
    font-size: var(--font-size-lg);
}
