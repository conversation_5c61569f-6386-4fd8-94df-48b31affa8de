{% extends "admin_base.html" %}

{% block title %}Manage Parent Categories{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Manage Parent Categories</h1>
        <div>
            <a href="{{ url_for('upload_file') }}" class="btn btn-info btn-sm me-2">
                <i class="fas fa-upload"></i> Back to Upload
            </a>
            <a href="{{ url_for('admin.admin_dashboard') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Parent Categories List -->
    <div class="row">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Parent Categories</h6>
                    <div>
                        <button type="button" class="btn btn-success btn-sm me-2" data-bs-toggle="modal" data-bs-target="#createChildModal">
                            <i class="fas fa-plus"></i> Add Child Category
                        </button>
                        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#createParentModal">
                            <i class="fas fa-folder-plus"></i> Add Parent Category
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if parent_categories %}
                        <div class="table-responsive">
                            <table class="table table-bordered" id="parentCategoriesTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Child Categories</th>
                                        <th>Display Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for parent in parent_categories %}
                                    <tr>
                                        <td><strong>{{ parent.name }}</strong></td>
                                        <td>{{ parent.description or 'No description' }}</td>
                                        <td>
                                            {% if parent.child_categories %}
                                                {% for child in parent.child_categories %}
                                                    <span class="badge badge-info me-1">
                                                        {{ child }}
                                                        <button class="btn btn-sm btn-outline-danger remove-child-btn" 
                                                                data-parent-id="{{ parent.id }}" 
                                                                data-parent-name="{{ parent.name|e }}" 
                                                                data-child-name="{{ child|e }}" 
                                                                title="Remove Child Category"
                                                                style="border: none; background: none; color: inherit; padding: 0; margin-left: 5px;">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </span>
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">No child categories</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ parent.display_order }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success add-child-btn" data-parent-name="{{ parent.name|e }}" title="Add Child Category">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary edit-parent-btn" data-parent-id="{{ parent.id }}" title="Edit Parent Category">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger delete-parent-btn" data-parent-id="{{ parent.id }}" data-parent-name="{{ parent.name|e }}" title="Delete Parent Category">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-gray-300 mb-3"></i>
                            <p class="text-gray-500">No parent categories found.</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createParentModal">
                                <i class="fas fa-folder-plus"></i> Create First Parent Category
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Category Assignment -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Assign Existing Categories</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="action" value="assign_category">
                        <div class="form-group">
                            <label for="category_name">Category</label>
                            <select class="form-control" id="category_name" name="category_name" required>
                                <option value="">Select a category</option>
                                {% for category in all_categories %}
                                    <option value="{{ category }}">{{ category }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="parent_category_name">Parent Category</label>
                            <select class="form-control" id="parent_category_name" name="parent_category_name" required>
                                <option value="">Select a parent category</option>
                                {% for parent in parent_categories %}
                                    <option value="{{ parent.name }}">{{ parent.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-link"></i> Assign Category
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Parent Category Modal -->
<div class="modal fade" id="createParentModal" tabindex="-1" role="dialog" aria-labelledby="createParentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createParentModalLabel">Create Parent Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_parent">
                    <div class="form-group">
                        <label for="name">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="display_order">Display Order</label>
                        <input type="number" class="form-control" id="display_order" name="display_order" value="0" min="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Parent Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Child Category Modal -->
<div class="modal fade" id="createChildModal" tabindex="-1" role="dialog" aria-labelledby="createChildModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createChildModalLabel">Create Child Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_child">
                    <div class="form-group">
                        <label for="child_name">Child Category Name</label>
                        <input type="text" class="form-control" id="child_name" name="child_name" required>
                    </div>
                    <div class="form-group">
                        <label for="child_description">Description</label>
                        <textarea class="form-control" id="child_description" name="child_description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="child_parent_category">Parent Category</label>
                        <select class="form-control" id="child_parent_category" name="child_parent_category" required>
                            <option value="">Select a parent category</option>
                            {% for parent in parent_categories %}
                                <option value="{{ parent.name }}">{{ parent.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Create Child Category</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Parent Category Modal -->
<div class="modal fade" id="editParentModal" tabindex="-1" role="dialog" aria-labelledby="editParentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editParentModalLabel">Edit Parent Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_parent">
                    <input type="hidden" name="parent_id" id="edit_parent_id">
                    <div class="form-group">
                        <label for="edit_name">Name</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_description">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="edit_display_order">Display Order</label>
                        <input type="number" class="form-control" id="edit_display_order" name="display_order" min="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Parent Category</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize DataTable with enhanced functionality
    initializeDataTable();
    
    // Handle add child category button clicks
    $('.add-child-btn').on('click', function() {
        var parentName = $(this).data('parent-name');
        $('#child_parent_category').val(parentName);
        var createChildModal = new bootstrap.Modal(document.getElementById('createChildModal'));
        createChildModal.show();
    });
    
    // Handle edit parent category button clicks
    $('.edit-parent-btn').on('click', function() {
        var parentId = $(this).data('parent-id');
        loadParentCategoryData(parentId);
    });

    // Handle delete parent category button clicks
    $('.delete-parent-btn').on('click', function() {
        var parentId = $(this).data('parent-id');
        var parentName = $(this).data('parent-name');
        if (confirm('Are you sure you want to delete parent category "' + parentName + '"? This action cannot be undone.')) {
            deleteParentCategory(parentId, parentName);
        }
    });

    // Handle remove child category button clicks
    $(document).on('click', '.remove-child-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        var parentId = $(this).data('parent-id');
        var parentName = $(this).data('parent-name');
        var childName = $(this).data('child-name');
        
        if (confirm('Are you sure you want to remove child category "' + childName + '" from parent category "' + parentName + '"? This will unassign the child category from the parent.')) {
            removeChildFromParent(parentId, childName, parentName);
        }
    });
    
    // Handle form submissions with loading states
    $('form').on('submit', function() {
        var submitBtn = $(this).find('button[type="submit"]');
        var originalText = submitBtn.text();
        submitBtn.prop('disabled', true).text('Processing...');
        
        // Re-enable button after 5 seconds as fallback
        setTimeout(function() {
            submitBtn.prop('disabled', false).text(originalText);
        }, 5000);
    });

    // Handle modal close events
    $('.modal').on('hidden.bs.modal', function() {
        // Reset forms when modals are closed
        $(this).find('form')[0].reset();
    });
});

function initializeDataTable() {
    if ($.fn.DataTable && $('#parentCategoriesTable').length) {
        try {
            var table = $('#parentCategoriesTable').DataTable({
                "order": [[3, "asc"]], // Sort by display order
                "pageLength": 25,
                "responsive": true,
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "emptyTable": "No data available in table"
                },
                "columnDefs": [
                    {
                        "targets": -1, // Last column (actions)
                        "orderable": false,
                        "searchable": false
                    }
                ],
                "dom": '<"top"lf>rt<"bottom"ip><"clear">',
                "initComplete": function() {
                    console.log('DataTable initialized successfully');
                }
            });
            
            // Add custom search functionality
            $('#parentCategoriesTable_filter input').on('keyup', function() {
                table.search(this.value).draw();
            });
            
        } catch (error) {
            console.warn('DataTable initialization failed:', error);
            // Fallback: add basic table styling
            $('#parentCategoriesTable').addClass('table-striped table-hover');
        }
    } else {
        console.warn('DataTable library not loaded or table not found');
        // Fallback: add basic table styling
        $('#parentCategoriesTable').addClass('table-striped table-hover');
    }
}

function loadParentCategoryData(parentId) {
    if (!parentId) {
        console.error('Parent ID is required');
        showAlert('Error: Parent ID is required', 'error');
        return;
    }
    
    // Show loading state
    var editModal = new bootstrap.Modal(document.getElementById('editParentModal'));
    editModal.show();
    $('#editParentModal .modal-body').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
    
    // Fetch parent category data
    $.ajax({
        url: `/api/parent-categories/${parentId}`,
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            if (response.success && response.data) {
                populateEditModal(response.data);
            } else {
                showAlert('Error: ' + (response.error || 'Failed to load parent category data'), 'error');
                editModal.hide();
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching parent category data:', error);
            var errorMsg = 'Failed to load parent category data';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showAlert('Error: ' + errorMsg, 'error');
            editModal.hide();
        }
    });
}

function populateEditModal(parentData) {
    // Restore the modal body with the form
    var modalBody = `
        <form method="POST">
            <input type="hidden" name="action" value="edit_parent">
            <input type="hidden" name="parent_id" id="edit_parent_id" value="${parentData.id}">
            <div class="form-group">
                <label for="edit_name">Name</label>
                <input type="text" class="form-control" id="edit_name" name="name" value="${escapeHtml(parentData.name)}" required>
            </div>
            <div class="form-group">
                <label for="edit_description">Description</label>
                <textarea class="form-control" id="edit_description" name="description" rows="3">${escapeHtml(parentData.description || '')}</textarea>
            </div>
            <div class="form-group">
                <label for="edit_display_order">Display Order</label>
                <input type="number" class="form-control" id="edit_display_order" name="display_order" value="${parentData.display_order || 0}" min="0">
            </div>
        </form>
    `;
    
    $('#editParentModal .modal-body').html(modalBody);
}

function editParentCategory(parentId) {
    loadParentCategoryData(parentId);
}

function addChildToParent(parentName) {
    // Set the parent category in the create child modal
    $('#child_parent_category').val(parentName);
    var createChildModal = new bootstrap.Modal(document.getElementById('createChildModal'));
    createChildModal.show();
}

function removeChildFromParent(parentId, childName, parentName) {
    // Show loading state
    showAlert('Removing child category "' + childName + '" from parent category "' + parentName + '"...', 'info');
    
    // URL encode the child name to handle special characters
    var encodedChildName = encodeURIComponent(childName);
    
    // Get CSRF token from meta tag
    var csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    $.ajax({
        url: `/api/parent-categories/${parentId}/children/${encodedChildName}`,
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                showAlert('Child category "' + childName + '" removed from parent category "' + parentName + '" successfully.', 'success');
                // Reload the page to refresh the table
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Error removing child category: ' + (response.error || 'Unknown error'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error removing child category:', error);
            var errorMsg = 'Failed to remove child category';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showAlert('Error: ' + errorMsg, 'error');
        }
    });
}

function deleteParentCategory(parentId, parentName) {
    // Show loading state
    showAlert('Deleting parent category "' + parentName + '"...', 'info');
    
    // Get CSRF token from meta tag
    var csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    
    $.ajax({
        url: `/api/parent-categories/${parentId}`,
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        success: function(response) {
            if (response.success) {
                showAlert('Parent category "' + parentName + '" deleted successfully.', 'success');
                // Reload the page to refresh the table
                setTimeout(function() {
                    location.reload();
                }, 1500);
            } else {
                showAlert('Error deleting parent category: ' + (response.error || 'Unknown error'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error deleting parent category:', error);
            var errorMsg = 'Failed to delete parent category';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            }
            showAlert('Error: ' + errorMsg, 'error');
        }
    });
}

function showAlert(message, type) {
    // Create alert element
    var alertClass = 'alert-info';
    if (type === 'error') {
        alertClass = 'alert-danger';
    } else if (type === 'success') {
        alertClass = 'alert-success';
    } else if (type === 'warning') {
        alertClass = 'alert-warning';
    }
    
    var alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    // Insert alert at the top of the container
    $('.container-fluid').prepend(alertHtml);
    
    // Auto-dismiss after 5 seconds (except for errors)
    if (type !== 'error') {
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
}

function escapeHtml(text) {
    if (!text) return '';
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Handle form validation
$(document).on('submit', '#editParentModal form', function(e) {
    var name = $('#edit_name').val().trim();
    if (!name) {
        e.preventDefault();
        showAlert('Error: Parent category name is required', 'error');
        return false;
    }
});
</script>
{% endblock %}
